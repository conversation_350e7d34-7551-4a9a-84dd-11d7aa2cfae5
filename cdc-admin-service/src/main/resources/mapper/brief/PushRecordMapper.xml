<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.iflytek.cdc.admin.mapper.brief.PushRecordMapper">
    <select id="reportPushStats" resultType="com.iflytek.cdc.admin.vo.brief.BriefReportStatsVo">

        select
        to_char(r.create_time, 'YYYY-MM-DD') as createDate,
        count(r.id) as reportCnt,
        count(r.id) as reportSuccessCnt,
        0 as reportFailCnt
        from
        tb_cdcbr_push_record r
        join tb_cdcbr_brief_info i on
        r.brief_id = i.id
        join tb_cdcbr_template t on
        i.template_id = t.id

        where 1 = 1

        <if test="statisticsCycle != null and statisticsCycle != ''">
            and t.statistics_cycle = #{statisticsCycle}
        </if>
        <if test="startDate != null ">
            and r.create_time &gt;= #{startDate}
        </if>
        <if test="endDate != null ">
            and r.create_time &lt;= #{endDate}
        </if>
        <if test="provinceCode != null and provinceCode != ''">
            and r.province_code = #{provinceCode}
        </if>
        <if test="cityCode != null and cityCode != ''">
            and r.city_code = #{cityCode}
        </if>
        <if test="districtCode != null and districtCode != ''">
            and r.district_code = #{districtCode}
        </if>
        group by to_char(r.create_time, 'YYYY-MM-DD')
        order by to_char(r.create_time, 'YYYY-MM-DD') asc
    </select>
</mapper>