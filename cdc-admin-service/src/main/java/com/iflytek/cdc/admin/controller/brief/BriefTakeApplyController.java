package com.iflytek.cdc.admin.controller.brief;

import com.github.pagehelper.PageInfo;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyApproveDto;
import com.iflytek.cdc.admin.dto.brief.BriefTakeApplyQueryDto;
import com.iflytek.cdc.admin.entity.brief.BriefTakeApply;
import com.iflytek.cdc.admin.service.brief.BriefTakeApplyService;
import com.iflytek.cdc.admin.vo.brief.BriefTakeApplyVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@Api(tags = "报告订阅申请")
@RestController
@RequestMapping("/pt/{version}/brief/take")
@RequiredArgsConstructor
public class BriefTakeApplyController {

    private BriefTakeApplyService briefTakeApplyService;

    @Autowired
    public void setBriefTakeApplyService(BriefTakeApplyService briefTakeApplyService) {
        this.briefTakeApplyService = briefTakeApplyService;
    }

    @ApiOperation("创建报告订阅申请")
    @PostMapping("/create")
    public BriefTakeApply create(@RequestBody BriefTakeApply briefTakeApply,
                                         @RequestParam String loginUserId){
        return briefTakeApplyService.create(briefTakeApply,loginUserId);
    }

    @ApiOperation("查询报告订阅申请")
    @GetMapping("/queryById")
    public BriefTakeApplyVo queryById(@RequestParam String id,
                                      @RequestParam String loginUserId){
        return briefTakeApplyService.queryById(id,loginUserId);
    }

    @ApiOperation("报告订阅申请")
    @PostMapping("/queryList")
    public PageInfo<BriefTakeApplyVo> queryList(@RequestBody BriefTakeApplyQueryDto dto,
                                               @RequestParam String loginUserId){
        return briefTakeApplyService.queryList(dto,loginUserId);
    }

    @ApiOperation("审批报告订阅申请")
    @PostMapping("/updateApprove")
    public Boolean updateApprove(@RequestBody BriefTakeApplyApproveDto dto,
                                 @RequestParam String loginUserId){
        return briefTakeApplyService.updateApprove(dto,loginUserId);
    }

}
