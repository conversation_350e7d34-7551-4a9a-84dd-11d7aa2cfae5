package com.iflytek.cdc.admin.service.brief;

import com.iflytek.cdc.admin.dto.brief.BriefReportStatsDto;
import com.iflytek.cdc.admin.mapper.brief.PushRecordMapper;
import com.iflytek.cdc.admin.vo.brief.BriefReportStatsVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class BriefReportStatsServiceImpl implements BriefReportStatsService{


    private PushRecordMapper pushRecordMapper;

    @Autowired
    public void setPushRecordMapper(PushRecordMapper pushRecordMapper) {
        this.pushRecordMapper = pushRecordMapper;
    }

    @Override
    public List<BriefReportStatsVo> reportPushStats(BriefReportStatsDto dto) {
        // 获取数据库查询结果
        List<BriefReportStatsVo> list = pushRecordMapper.reportPushStats(dto);

        Date startDate = dto.getStartDate();
        Date endDate = dto.getEndDate();

        // 如果没有指定时间范围，直接返回查询结果
        if (startDate == null || endDate == null) {
            return list;
        }

        // 补全时间段数据
        return fillMissingDates(list, startDate, endDate);
    }

    /**
     * 补全缺失的日期数据
     *
     * @param dataList 数据库查询结果
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 补全后的完整数据列表
     */
    private List<BriefReportStatsVo> fillMissingDates(List<BriefReportStatsVo> dataList, Date startDate, Date endDate) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        // 转换为LocalDate进行日期计算
        LocalDate start = startDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDate();

        // 生成完整的日期列表
        List<LocalDate> allDates = new ArrayList<>();
        LocalDate current = start;
        while (!current.isAfter(end)) {
            allDates.add(current);
            current = current.plusDays(1);
        }

        // 将查询结果转换为Map，以日期为key
        Map<String, BriefReportStatsVo> dataMap = dataList.stream()
                .collect(Collectors.toMap(BriefReportStatsVo::getCreateDate, vo -> vo, (v1, v2) -> v1));

        // 补全缺失的日期数据
        List<BriefReportStatsVo> result = new ArrayList<>();
        for (LocalDate date : allDates) {
            String dateStr = date.format(formatter);
            BriefReportStatsVo vo = dataMap.get(dateStr);

            if (vo == null) {
                // 创建空数据记录
                vo = new BriefReportStatsVo();
                vo.setCreateDate(dateStr);
                vo.setReportCnt(0);
                vo.setReportSuccessCnt(0);
                vo.setReportFailCnt(0);
            }

            result.add(vo);
        }

        return result;
    }
}
